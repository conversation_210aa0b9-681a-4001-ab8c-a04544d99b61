import request from '@/utils/request'

// 查询订单发货列表
export function listVimOrderClaims(query) {
  return request({
    url: '/VimOrderClaimSys/VimOrderClaims/list',
    method: 'get',
    params: query
  })
}

// 查询订单发货详细
export function getVimOrderClaims(id) {
  return request({
    url: '/VimOrderClaimSys/VimOrderClaims/' + id,
    method: 'get'
  })
}

// 新增订单发货
export function addVimOrderClaims(data) {
  return request({
    url: '/VimOrderClaimSys/VimOrderClaims',
    method: 'post',
    data: data
  })
}

// 修改订单发货
export function updateVimOrderClaims(data) {
  return request({
    url: '/VimOrderClaimSys/VimOrderClaims',
    method: 'put',
    data: data
  })
}

// 删除订单发货
export function delVimOrderClaims(id) {
  return request({
    url: '/VimOrderClaimSys/VimOrderClaims/' + id,
    method: 'delete'
  })
}

// 更新订单自动发货状态
export function updateAutoStatus(data) {
  return request({
    url: '/VimOrderClaimSys/VimOrderClaims/updateAuto',
    method: 'put',
    data: data
  })
}
