<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.VimBlackSys.mapper.VimBlackMapper">
    
    <resultMap type="VimBlack" id="VimBlackResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="price"    column="price"    />
        <result property="endTime"    column="end_time"    />
        <result property="state"    column="state"    />
    </resultMap>

    <sql id="selectVimBlackVo">
        select id, name, price, end_time, state from vim_black
    </sql>

    <select id="selectVimBlackList" parameterType="VimBlack" resultMap="VimBlackResult">
        <include refid="selectVimBlackVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="state != null "> and state = #{state}</if>
        </where>
    </select>
    
    <select id="selectVimBlackById" parameterType="Long" resultMap="VimBlackResult">
        <include refid="selectVimBlackVo"/>
        where id = #{id}
    </select>

    <insert id="insertVimBlack" parameterType="VimBlack">
        insert into vim_black
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="price != null">price,</if>
            <if test="endTime != null">end_time,</if>
            <if test="state != null">state,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="price != null">#{price},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="state != null">#{state},</if>
         </trim>
    </insert>

    <update id="updateVimBlack" parameterType="VimBlack">
        update vim_black
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="price != null">price = #{price},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="state != null">state = #{state},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimBlackById" parameterType="Long">
        delete from vim_black where id = #{id}
    </delete>

    <delete id="deleteVimBlackByIds" parameterType="String">
        delete from vim_black where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>