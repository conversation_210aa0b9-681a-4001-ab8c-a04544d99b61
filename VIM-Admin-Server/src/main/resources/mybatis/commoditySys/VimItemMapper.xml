<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.commoditySys.mapper.VimItemMapper">
    
    <resultMap type="VimItem" id="VimItemResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="hashname"    column="hashname"    />
        <result property="tag"    column="tag"    />
        <result property="priceShow"    column="price_show"    />
        <result property="priceCost"    column="price_cost"    />
        <result property="priceBuy"    column="price_buy"    />
        <result property="priceRecycle"    column="price_recycle"    />
        <result property="image"    column="image"    />
        <result property="sale"    column="sale"    />
        <result property="stock"    column="stock"    />
        <result property="CreateTimeStamp"    column="create_time"    />
        <result property="updateTimeStamp"    column="update_time"    />
        <result property="deleteTime"    column="delete_time"    />
    </resultMap>
    
    <!-- 用户背包物品结果映射 -->
    <resultMap type="VimItem" id="BackpackItemResult">
        <result property="id"    column="itemid"    />
        <result property="name"    column="itemname"    />
        <result property="image"    column="image"    />
        <result property="tag"    column="tag"    />
        <result property="priceShow"    column="price_show"    />
        <!-- 背包特有字段 -->
        <result property="count"    column="count"    />
        <!-- 使用count字段存储物品数量 -->
    </resultMap>

    <sql id="selectVimItemVo">
        select id, name,hashname, tag, price_show, price_cost, price_buy, price_recycle, image, sale, stock, create_time, update_time, delete_time from vim_item
    </sql>

    <select id="selectAllVimItemList" resultMap="VimItemResult">
        select v.id, v.name,v.image from vim_item v where v.delete_time is null
    </select>
    
    <select id="selectVimItemList" parameterType="VimItem" resultMap="VimItemResult">
        <include refid="selectVimItemVo"/>
        <where>
            <if test="id != null "> and id = #{id}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="tag != null  and tag != ''"> and tag like concat('%', #{tag}, '%')</if>
            <if test="sale != null "> and sale = #{sale}</if>
            <!-- 价格范围筛选 -->
            <if test="params != null">
                <if test="params.minPrice != null and params.minPrice != ''"> and price_show >= #{params.minPrice}</if>
                <if test="params.maxPrice != null and params.maxPrice != ''"> and price_show &lt;= #{params.maxPrice}</if>
            </if>
            and delete_time is  null
        </where>
        order by id desc
    </select>

    <select id="selectVimItemByIds" parameterType="java.util.List" resultMap="VimItemResult">
        <include refid="selectVimItemVo"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND delete_time IS NULL
    </select>

    <select id="selectVimItemById" parameterType="Long" resultMap="VimItemResult">
        <include refid="selectVimItemVo"/>
        where id = #{id}
    </select>

    <select id="selectVimItemByItemName" parameterType="String" resultMap="VimItemResult">
        <include refid="selectVimItemVo" />
        <where>
            <if test="name != null and name != ''">
                name = #{name}
            </if>
            and delete_time is null
        </where>
    </select>

    <select id="selectVimItemByUserId" resultMap="BackpackItemResult">
        SELECT
        o.itemid,
        MAX(o.itemname) AS itemname,
        MAX(i.image) AS image,
        MAX(i.tag) AS tag,
        COUNT(o.itemid) AS count,
        MAX(i.price_show) AS price_show,
        o.itemlevel
        FROM
        vim_order_box o
        JOIN vim_item i ON o.itemid = i.id
        WHERE
        o.uid = #{userId}
        AND o.state = 1
        <if test="itemName != null and itemName != ''">
            AND o.itemname LIKE CONCAT(#{itemName}, '%')
        </if>
        GROUP BY
        o.itemid, o.itemlevel
        <choose>
            <when test="sortField == 'priceDesc'">
                ORDER BY MAX(i.price_show) DESC
            </when>
            <when test="sortField == 'priceAsc'">
                ORDER BY MAX(i.price_show) ASC
            </when>
            <when test="sortField == 'timeDesc'">
                ORDER BY MAX(o.timestamp) DESC
            </when>
            <otherwise>
                ORDER BY o.itemlevel DESC, MAX(i.price_show) DESC
            </otherwise>
        </choose>
    </select>


    <insert id="insertVimItem" parameterType="VimItem" useGeneratedKeys="true" keyProperty="id">
        insert into vim_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="hashname != null and hashname != ''">hashname,</if>
            <if test="tag != null and tag != ''">tag,</if>
            <if test="priceShow != null">price_show,</if>
            <if test="priceCost != null">price_cost,</if>
            <if test="priceBuy != null">price_buy,</if>
            <if test="priceRecycle != null">price_recycle,</if>
            <if test="image != null and image != ''">image,</if>
            <if test="sale != null">sale,</if>
            <if test="stock != null">stock,</if>
            <if test="CreateTimeStamp != null">create_time,</if>
            <if test="updateTimeStamp != null">update_time,</if>
            <if test="deleteTime != null">delete_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="hashname != null and hashname != ''">#{hashname},</if>
            <if test="tag != null and tag != ''">#{tag},</if>
            <if test="priceShow != null">#{priceShow},</if>
            <if test="priceCost != null">#{priceCost},</if>
            <if test="priceBuy != null">#{priceBuy},</if>
            <if test="priceRecycle != null">#{priceRecycle},</if>
            <if test="image != null and image != ''">#{image},</if>
            <if test="sale != null">#{sale},</if>
            <if test="stock != null">#{stock},</if>
            <if test="CreateTimeStamp != null">#{CreateTimeStamp},</if>
            <if test="updateTimeStamp != null">#{updateTimeStamp},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
         </trim>
    </insert>

    <update id="updateVimItem" parameterType="VimItem">
        update vim_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="hashname != null and hashname != ''">hashname = #{hashname},</if>
            <if test="tag != null and tag != ''">tag = #{tag},</if>
            <if test="priceShow != null">price_show = #{priceShow},</if>
            <if test="priceCost != null">price_cost = #{priceCost},</if>
            <if test="priceBuy != null">price_buy = #{priceBuy},</if>
            <if test="priceRecycle != null">price_recycle = #{priceRecycle},</if>
            <if test="image != null and image != ''">image = #{image},</if>
            <if test="sale != null">sale = #{sale},</if>
            <if test="stock != null">stock = #{stock},</if>
            <if test="CreateTimeStamp != null">create_time = #{CreateTimeStamp},</if>
            <if test="updateTimeStamp != null">update_time = #{updateTimeStamp},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
        </trim>
        where id = #{id}
    </update>

<!--    <delete id="deleteVimItemById" parameterType="Long">-->
<!--        delete from vim_item where id = #{id}-->
<!--    </delete>-->

<!--    <delete id="deleteVimItemByIds" parameterType="String">-->
<!--        delete from vim_item where id in -->
<!--        <foreach item="id" collection="array" open="(" separator="," close=")">-->
<!--            #{id}-->
<!--        </foreach>-->
<!--    </delete>-->
<!--    调用删除时只是添加一个删除时间戳，且前端不显示-->
    <update id="deleteVimItemById" parameterType="Long">
        update vim_item
        set delete_time = #{deleteTime}
        where id = #{id}
    </update>

    <update id="deleteVimItemByIds" parameterType="java.util.List">
        update vim_item
        set delete_time = #{deleteTime}
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
