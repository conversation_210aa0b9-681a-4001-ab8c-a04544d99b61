<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.VimOrderClaimSys.mapper.VimOrderClaimMapper">
    
    <resultMap type="VimOrderClaim" id="VimOrderClaimResult">
        <result property="id"    column="id"    />
        <result property="oid"    column="oid"    />
        <result property="uid"    column="uid"    />
        <result property="itemid"    column="itemid"    />
        <result property="itemname"    column="itemname"    />
        <result property="hashname"    column="hashname"    />
        <result property="creatTime"    column="creat_time"    />
        <result property="claimTime"    column="claim_time"    />
        <result property="state"    column="state"    />
        <result property="steamid"    column="steamid"    />
        <result property="steamlink"    column="steamlink"    />
        <result property="info"    column="info"/>
        <result property="cost"    column="cost"/>
        <result property="auto"    column="auto"/>
    </resultMap>

    <!-- 订单发货扩展结果映射，包含用户信息 -->
    <resultMap type="com.ruoyi.project.VimOrderClaimSys.domain.vo.VimOrderClaimVO" id="VimOrderClaimVOResult" extends="VimOrderClaimResult">
        <result property="userNickname"    column="user_nickname"    />
        <result property="userPhoneRaw"    column="user_phone"    />
        <result property="itemPrice"    column="price_cost"    />
        <result property="userIdentity"    column="user_identity"    />
        <!-- 🆕 新增用户等级和经验字段 -->
        <result property="userLevel"    column="user_level"    />
        <result property="userExp"    column="user_exp"    />
    </resultMap>
    <sql id="selectVimOrderClaimVo">
        select id, oid, uid, itemid, itemname, hashname, creat_time, claim_time, state, steamid, steamlink, info, cost, auto from vim_order_claim
    </sql>

    <!-- 包含用户信息的订单发货查询SQL -->
    <sql id="selectVimOrderClaimWithUserVo">
        SELECT
            voc.id,
            voc.oid,
            voc.uid,
            voc.itemid,
            voc.itemname,
            voc.hashname,
            voc.creat_time,
            voc.claim_time,
            voc.state,
            voc.steamid,
            voc.steamlink,
            voc.info,
            voc.cost,
            voc.auto,
            vu.nickname AS user_nickname,
            vu.phone AS user_phone,
            vu.identity AS user_identity,
            <!--  新增用户等级和经验字段 -->
            vu.level AS user_level,
            vu.exp AS user_exp,
            vi.price_cost
        FROM
            vim_order_claim voc
                LEFT JOIN vim_user vu ON voc.uid = vu.id
                LEFT JOIN vim_item vi on voc.itemid = vi.id
    </sql>

    <select id="selectVimOrderClaimList" parameterType="VimOrderClaim" resultMap="VimOrderClaimVOResult">
        <include refid="selectVimOrderClaimWithUserVo"/>
        <where>
            <if test="id != null  and id != ''"> and voc.id = #{id}</if>
            <if test="oid != null  and oid != ''"> and voc.oid = #{oid}</if>
            <if test="uid != null "> and voc.uid = #{uid}</if>
            <if test="state != null "> and voc.state = #{state}</if>
            <if test="userPhone != null and userPhone != ''"> and vu.phone LIKE CONCAT('%', #{userPhone}, '%')</if>
            <if test="beginClaimTime != null"> and voc.claim_time &gt;= #{beginClaimTime}</if>
            <if test="endClaimTime != null"> and voc.claim_time &lt;= #{endClaimTime}</if>
        </where>
        <choose>
            <when test="params.orderBy != null and params.orderBy == 'claimTimeDesc'">
                ORDER BY voc.claim_time DESC
            </when>
            <when test="params.orderBy != null and params.orderBy == 'userLevelAsc'">
                ORDER BY vu.level ASC, voc.creat_time DESC
            </when>
            <when test="params.orderBy != null and params.orderBy == 'userLevelDesc'">
                ORDER BY vu.level DESC, voc.creat_time DESC
            </when>
            <otherwise>
                ORDER BY voc.creat_time DESC
            </otherwise>
        </choose>
    </select>
    
    <select id="selectVimOrderClaimById" parameterType="String" resultMap="VimOrderClaimResult">
        <include refid="selectVimOrderClaimVo"/>
        where id = #{id}
    </select>

    <insert id="insertVimOrderClaim" parameterType="VimOrderClaim">
        insert into vim_order_claim
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="oid != null">oid,</if>
            <if test="uid != null">uid,</if>
            <if test="itemid != null">itemid,</if>
            <if test="itemname != null">itemname,</if>
            <if test="hashname != null">hashname,</if>
            <if test="creatTime != null">creat_time,</if>
            <if test="claimTime != null">claim_time,</if>
            <if test="state != null">state,</if>
            <if test="steamid != null">steamid,</if>
            <if test="steamlink != null">steamlink,</if>
            <if test="info != null">info,</if>
            <if test="cost != null">cost,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="oid != null">#{oid},</if>
            <if test="uid != null">#{uid},</if>
            <if test="itemid != null">#{itemid},</if>
            <if test="itemname != null">#{itemname},</if>
            <if test="hashname != null">#{hashname},</if>
            <if test="creatTime != null">#{creatTime},</if>
            <if test="claimTime != null">#{claimTime},</if>
            <if test="state != null">#{state},</if>
            <if test="steamid != null">#{steamid},</if>
            <if test="steamlink != null">#{steamlink},</if>
            <if test="info != null">#{info},</if>
            <if test="cost != null">#{cost},</if>
         </trim>
    </insert>

    <update id="updateVimOrderClaim" parameterType="VimOrderClaim">
        update vim_order_claim
        <trim prefix="SET" suffixOverrides=",">
            <if test="oid != null">oid = #{oid},</if>
            <if test="uid != null">uid = #{uid},</if>
            <if test="itemid != null">itemid = #{itemid},</if>
            <if test="itemname != null">itemname = #{itemname},</if>
            <if test="hashname != null">hashname = #{hashname},</if>
            <if test="creatTime != null">creat_time = #{creatTime},</if>
            <if test="claimTime != null">claim_time = #{claimTime},</if>
            <if test="state != null">state = #{state},</if>
            <if test="steamid != null">steamid = #{steamid},</if>
            <if test="steamlink != null">steamlink = #{steamlink},</if>
            <if test="info != null">info = #{info},</if>
            <if test="cost != null">cost = #{cost},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimOrderClaimById" parameterType="String">
        delete from vim_order_claim where id = #{id}
    </delete>

    <delete id="deleteVimOrderClaimByIds" parameterType="String">
        delete from vim_order_claim where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>