<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimBanner.mapper.VimBannerMapper">
    
    <resultMap type="VimBanner" id="VimBannerResult">
        <result property="id"    column="id"    />
        <result property="image"    column="image"    />
        <result property="state"    column="state"    />
        <result property="sort"    column="sort"    />
    </resultMap>

    <sql id="selectVimBannerVo">
        select id, image, state, sort from vim_banner
    </sql>

    <select id="selectVimBannerList" parameterType="VimBanner" resultMap="VimBannerResult">
        <include refid="selectVimBannerVo"/>
        <where>  
            <if test="image != null  and image != ''"> and image like concat('%', #{image}, '%')</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
        order by sort asc
    </select>
    
    <select id="selectVimBannerById" parameterType="Integer" resultMap="VimBannerResult">
        <include refid="selectVimBannerVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertVimBanner" parameterType="VimBanner" useGeneratedKeys="true" keyProperty="id">
        insert into vim_banner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="image != null">image,</if>
            <if test="state != null">state,</if>
            <if test="sort != null">sort,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="image != null">#{image},</if>
            <if test="state != null">#{state},</if>
            <if test="sort != null">#{sort},</if>
         </trim>
    </insert>

    <update id="updateVimBanner" parameterType="VimBanner">
        update vim_banner
        <trim prefix="SET" suffixOverrides=",">
            <if test="image != null">image = #{image},</if>
            <if test="state != null">state = #{state},</if>
            <if test="sort != null">sort = #{sort},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimBannerById" parameterType="Integer">
        delete from vim_banner where id = #{id}
    </delete>

    <delete id="deleteVimBannerByIds" parameterType="String">
        delete from vim_banner where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 