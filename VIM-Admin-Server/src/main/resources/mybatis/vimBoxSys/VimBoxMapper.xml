<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimBoxSys.mapper.VimBoxMapper">
    
    <resultMap type="VimBox" id="VimBoxResult">
        <result property="id"    column="id"    />
        <result property="vimBoxtype"    column="type"    />
        <result property="name"    column="name"    />
        <result property="imageBox"    column="image_box"    />
        <result property="imageItem"    column="image_item"    />
        <result property="price"    column="price"    />
        <result property="sale"    column="sale"    />
        <result property="saleVolume"    column="sale_volume"    />
        <result property="saleAmount"    column="sale_amount"    />
        <result property="featured"    column="featured"    />
        <result property="level"    column="level"    />
        <result property="vimBoxcreateTime"    column="create_time"    />
        <result property="vimBoxupdateTime"    column="update_time"    />
        <result property="vimBoxdeleteTime"    column="delete_time"    />
        <result property="open"                column="open"/>
        <result property="battle"              column="battle"/>
    </resultMap>

    <sql id="selectVimBoxVo">
        SELECT
        vib.id,vib.type,vib.NAME,vib.image_box,vib.image_item,vib.price,vib.sale,
        vib.sale_volume,vib.sale_amount,vib.featured,vib.LEVEL,
        vib.create_time,vib.update_time,vib.delete_time,vit.open,vit.battle
        FROM
        vim_box vib
        LEFT JOIN vim_box_type vit ON vib.type = vit.NAME
    </sql>

    <select id="selectVimBoxList" parameterType="VimBox" resultMap="VimBoxResult">
        SELECT
        vib.id,vib.type,vib.NAME,vib.image_box,vib.image_item,vib.price,vib.sale,
        vib.sale_volume,vib.sale_amount,vib.featured,vib.LEVEL,
        vib.create_time,vib.update_time,vib.delete_time,vit.open,vit.battle
        FROM
        vim_box vib
        LEFT JOIN vim_box_type vit ON vib.type = vit.name
        <where>
            <if test="vimBoxtype != null  and vimBoxtype != ''"> and vib.type like concat('%', #{vimBoxtype}, '%')</if>
            <if test="name != null  and name != ''"> and vib.name like concat('%', #{name}, '%')</if>
            <if test="sale != null "> and vib.sale = #{sale}</if>
            <if test="featured != null "> and vib.featured = #{featured}</if>
            <if test="open != null "> and vit.open = #{open}</if>
            <if test="battle != null "> and vit.battle = #{battle}</if>
            and vib.delete_time is null
        </where>
        ORDER BY vib.type ASC, vib.price DESC
    </select>

    <select id="selectVimBoxById" parameterType="Long" resultMap="VimBoxResult">
        SELECT
        vib.id,vib.type,vib.NAME,vib.image_box,vib.image_item,vib.price,vib.sale,
        vib.sale_volume,vib.sale_amount,vib.featured,vib.LEVEL,
        vib.create_time,vib.update_time,vib.delete_time,vit.open,vit.battle
        FROM
        vim_box vib
        LEFT JOIN vim_box_type vit ON vib.type = vit.name
        where vib.id = #{id}
    </select>

    <insert id="insertVimBox" parameterType="VimBox" useGeneratedKeys="true" keyProperty="id">
        insert into vim_box
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vimBoxtype != null and vimBoxtype != ''">type,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="imageBox != null and imageBox != ''">image_box,</if>
            <if test="imageItem != null and imageItem != ''">image_item,</if>
            <if test="price != null">price,</if>
            <if test="sale != null">sale,</if>
            <if test="saleVolume != null">sale_volume,</if>
            <if test="saleAmount != null">sale_amount,</if>
            <if test="featured != null">featured,</if>
            <if test="level != null">level,</if>
            <if test="vimBoxcreateTime != null">create_time,</if>
            <if test="vimBoxupdateTime != null">update_time,</if>
            <if test="vimBoxdeleteTime != null">delete_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vimBoxtype != null and vimBoxtype != ''">#{vimBoxtype},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="imageBox != null and imageBox != ''">#{imageBox},</if>
            <if test="imageItem != null and imageItem != ''">#{imageItem},</if>
            <if test="price != null">#{price},</if>
            <if test="sale != null">#{sale},</if>
            <if test="saleVolume != null">#{saleVolume},</if>
            <if test="saleAmount != null">#{saleAmount},</if>
            <if test="featured != null">#{featured},</if>
            <if test="level != null">#{level},</if>
            <if test="vimBoxcreateTime != null">#{vimBoxcreateTime},</if>
            <if test="vimBoxupdateTime != null">#{vimBoxupdateTime},</if>
            <if test="vimBoxdeleteTime != null">#{vimBoxdeleteTime},</if>
         </trim>
    </insert>

    <update id="updateVimBox" parameterType="VimBox">
        update vim_box
        <trim prefix="SET" suffixOverrides=",">
            <if test="vimBoxtype != null and vimBoxtype != ''">type = #{vimBoxtype},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="imageBox != null and imageBox != ''">image_box = #{imageBox},</if>
            <if test="imageItem != null and imageItem != ''">image_item = #{imageItem},</if>
            <if test="price != null">price = #{price},</if>
            <if test="sale != null">sale = #{sale},</if>
            <if test="saleVolume != null">sale_volume = #{saleVolume},</if>
            <if test="saleAmount != null">sale_amount = #{saleAmount},</if>
            <if test="featured != null">featured = #{featured},</if>
            <if test="level != null">level = #{level},</if>
            <if test="vimBoxcreateTime != null">create_time = #{vimBoxcreateTime},</if>
            <if test="vimBoxupdateTime != null">update_time = #{vimBoxupdateTime},</if>
            <if test="vimBoxdeleteTime != null">delete_time = #{vimBoxdeleteTime},</if>
        </trim>
        where id = #{id}
    </update>

<!--    <delete id="deleteVimBoxById" parameterType="Long">-->
<!--        delete from vim_box where id = #{id}-->
<!--    </delete>-->

<!--    <delete id="deleteVimBoxByIds" parameterType="String">-->
<!--        delete from vim_box where id in -->
<!--    </delete>-->
    <update id="deleteVimBoxById" parameterType="java.util.Map">
        update vim_box
        set delete_time = #{deleteTime}
        where id = #{id}
    </update>

    <update id="deleteVimBoxByIds" parameterType="java.util.Map">
        update vim_box
        set delete_time = #{deleteTime}
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量查询盲盒及其商品信息的结果映射 -->
    <resultMap type="com.ruoyi.project.vimBoxSys.domain.VimBoxWithItemsDTO" id="VimBoxWithItemsResult">
        <result property="boxId" column="box_id" />
        <result property="boxName" column="box_name" />
        <result property="boxPrice" column="box_price" />
        <result property="itemId" column="item_id" />
        <result property="itemName" column="item_name" />
        <result property="itemPriceShow" column="item_price_show" />
        <result property="itemPriceRecycle" column="item_price_recycle" />
        <result property="probability" column="probability" />
        <result property="level" column="level" />
    </resultMap>

    <!-- 批量查询盲盒及其商品信息 -->
    <select id="selectVimBoxWithItemsByIds" parameterType="java.util.List" resultMap="VimBoxWithItemsResult">
        SELECT
            vb.id as box_id,
            vb.name as box_name,
            vb.price as box_price,
            vi.id as item_id,
            vi.name as item_name,
            vi.price_show as item_price_show,
            vi.price_recycle as item_price_recycle,
            vbi.probability as probability,
            vbi.level as level
        FROM vim_box vb
        LEFT JOIN vim_box_item vbi ON vb.id = vbi.id_box
        LEFT JOIN vim_item vi ON vbi.id_item = vi.id
        WHERE vb.id IN
        <foreach item="boxId" collection="list" open="(" separator="," close=")">
            #{boxId}
        </foreach>
        AND vb.delete_time IS NULL
        AND (vi.delete_time IS NULL OR vi.delete_time = 0)
        ORDER BY vb.id, vbi.id
    </select>
</mapper>