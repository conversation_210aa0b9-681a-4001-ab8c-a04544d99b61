<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimRoll.mapper.AutoRollRulerConfigMapper">
    
    <resultMap type="com.ruoyi.project.vimRoll.domain.AutoRollConfig$AutoRollRulerConfig" id="AutoRollRulerConfigResult">
        <result property="configId"    column="config_id"    />
        <result property="type"    column="type"    />
        <result property="value"    column="value"    />
    </resultMap>

    <select id="selectRulerConfigsByConfigId" parameterType="Long" resultMap="AutoRollRulerConfigResult">
        select config_id, type, value
        from sys_auto_roll_ruler_config
        where config_id = #{configId}
        order by sort_order
    </select>
        
    <insert id="insertRulerConfig" parameterType="com.ruoyi.project.vimRoll.domain.AutoRollConfig$AutoRollRulerConfig">
        insert into sys_auto_roll_ruler_config(config_id, type, value, sort_order)
        values (#{configId}, #{type}, #{value}, 0)
    </insert>

    <insert id="batchInsertRulerConfigs" parameterType="java.util.List">
        insert into sys_auto_roll_ruler_config(config_id, type, value, sort_order)
        values
        <foreach collection="list" item="ruler" separator=",">
            (#{ruler.configId}, #{ruler.type}, #{ruler.value}, 0)
        </foreach>
    </insert>

    <delete id="deleteRulerConfigsByConfigId" parameterType="Long">
        delete from sys_auto_roll_ruler_config where config_id = #{configId}
    </delete>
</mapper>
