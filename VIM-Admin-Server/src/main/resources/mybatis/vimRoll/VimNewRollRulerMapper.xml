<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimRoll.mapper.VimNewRollRulerMapper">
    
    <resultMap type="com.ruoyi.project.vimRoll.domain.VimNewRollRuler" id="VimNewRollRulerResult">
        <result property="id"    column="id"    />
        <result property="rollId"    column="roll_id"    />
        <result property="type"    column="type"    />
        <result property="value"    column="value"    />
    </resultMap>

    <sql id="selectVimNewRollRulerVo">
        select id, roll_id, type, value
        from vim_new_rollruler
    </sql>

    <select id="selectVimNewRollRulerList" parameterType="com.ruoyi.project.vimRoll.domain.VimNewRollRuler" resultMap="VimNewRollRulerResult">
        <include refid="selectVimNewRollRulerVo"/>
        <where>  
            <if test="rollId != null "> and roll_id = #{rollId}</if>
            <if test="type != null "> and type = #{type}</if>
        </where>
    </select>
    
    <select id="selectVimNewRollRulerById" parameterType="Long" resultMap="VimNewRollRulerResult">
        <include refid="selectVimNewRollRulerVo"/>
        where id = #{id}
    </select>
    
    <select id="selectVimNewRollRulersByRollId" parameterType="Long" resultMap="VimNewRollRulerResult">
        <include refid="selectVimNewRollRulerVo"/>
        where roll_id = #{rollId}
        order by type
    </select>
        
    <insert id="insertVimNewRollRuler" parameterType="com.ruoyi.project.vimRoll.domain.VimNewRollRuler" useGeneratedKeys="true" keyProperty="id">
        insert into vim_new_rollruler
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rollId != null">roll_id,</if>
            <if test="type != null">type,</if>
            <if test="value != null">value,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rollId != null">#{rollId},</if>
            <if test="type != null">#{type},</if>
            <if test="value != null">#{value},</if>
         </trim>
    </insert>

    <update id="updateVimNewRollRuler" parameterType="com.ruoyi.project.vimRoll.domain.VimNewRollRuler">
        update vim_new_rollruler
        <trim prefix="SET" suffixOverrides=",">
            <if test="rollId != null">roll_id = #{rollId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="value != null">value = #{value},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimNewRollRulerById" parameterType="Long">
        delete from vim_new_rollruler where id = #{id}
    </delete>

    <delete id="deleteVimNewRollRulerByIds" parameterType="String">
        delete from vim_new_rollruler where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteVimNewRollRulerByRollId" parameterType="Long">
        delete from vim_new_rollruler where roll_id = #{rollId}
    </delete>
</mapper> 