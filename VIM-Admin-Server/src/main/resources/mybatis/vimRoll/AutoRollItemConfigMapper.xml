<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimRoll.mapper.AutoRollItemConfigMapper">
    
    <resultMap type="com.ruoyi.project.vimRoll.domain.AutoRollConfig$AutoRollItemConfig" id="AutoRollItemConfigResult">
        <result property="configId"    column="config_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="itemLevel"    column="item_level"    />
        <result property="image"    column="image"    />
        <result property="itemName"    column="item_name"    />
        <result property="count"    column="count"    />
    </resultMap>

    <select id="selectItemConfigsByConfigId" parameterType="Long" resultMap="AutoRollItemConfigResult">
        select config_id, item_id, item_level, image, item_name, count
        from sys_auto_roll_item_config
        where config_id = #{configId}
        order by sort_order
    </select>
        
    <insert id="insertItemConfig" parameterType="com.ruoyi.project.vimRoll.domain.AutoRollConfig$AutoRollItemConfig">
        insert into sys_auto_roll_item_config(config_id, item_id, item_level, image, item_name, count, sort_order)
        values (#{configId}, #{itemId}, #{itemLevel}, #{image}, #{itemName}, #{count}, 0)
    </insert>

    <insert id="batchInsertItemConfigs" parameterType="java.util.List">
        insert into sys_auto_roll_item_config(config_id, item_id, item_level, image, item_name, count, sort_order)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.configId}, #{item.itemId}, #{item.itemLevel}, #{item.image}, #{item.itemName}, #{item.count}, 0)
        </foreach>
    </insert>

    <delete id="deleteItemConfigsByConfigId" parameterType="Long">
        delete from sys_auto_roll_item_config where config_id = #{configId}
    </delete>
</mapper>
