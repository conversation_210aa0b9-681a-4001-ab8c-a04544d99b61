<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimRoll.mapper.AutoRollConfigMapper">
    
    <resultMap type="com.ruoyi.project.vimRoll.domain.AutoRollConfig" id="AutoRollConfigResult">
        <result property="id"    column="id"    />
        <result property="configName"    column="config_name"    />
        <result property="enabled"    column="enabled"    />
        <result property="titleTemplate"    column="title_template"    />
        <result property="infoTemplate"    column="info_template"    />
        <result property="maxUsers"    column="max_users"    />
        <result property="drawDelayMinutes"    column="draw_delay_minutes"    />
        <result property="drawTime"    column="draw_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectAutoRollConfigVo">
        select id, config_name, enabled, title_template, info_template, max_users, draw_delay_minutes, draw_time, create_time, update_time, create_by, update_by, remark from sys_auto_roll_config
    </sql>

    <select id="selectAutoRollConfigList" parameterType="com.ruoyi.project.vimRoll.domain.AutoRollConfig" resultMap="AutoRollConfigResult">
        <include refid="selectAutoRollConfigVo"/>
        <where>  
            <if test="configName != null  and configName != ''"> and config_name like concat('%', #{configName}, '%')</if>
            <if test="enabled != null "> and enabled = #{enabled}</if>
            <if test="titleTemplate != null  and titleTemplate != ''"> and title_template like concat('%', #{titleTemplate}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectAutoRollConfigById" parameterType="Long" resultMap="AutoRollConfigResult">
        <include refid="selectAutoRollConfigVo"/>
        where id = #{id}
    </select>

    <select id="selectEnabledAutoRollConfigs" resultMap="AutoRollConfigResult">
        <include refid="selectAutoRollConfigVo"/>
        where enabled = 1
        order by create_time desc
    </select>
        
    <insert id="insertAutoRollConfig" parameterType="com.ruoyi.project.vimRoll.domain.AutoRollConfig" useGeneratedKeys="true" keyProperty="id">
        insert into sys_auto_roll_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configName != null and configName != ''">config_name,</if>
            <if test="enabled != null">enabled,</if>
            <if test="titleTemplate != null and titleTemplate != ''">title_template,</if>
            <if test="infoTemplate != null">info_template,</if>
            <if test="maxUsers != null">max_users,</if>
            <if test="drawDelayMinutes != null">draw_delay_minutes,</if>
            <if test="drawTime != null">draw_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configName != null and configName != ''">#{configName},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="titleTemplate != null and titleTemplate != ''">#{titleTemplate},</if>
            <if test="infoTemplate != null">#{infoTemplate},</if>
            <if test="maxUsers != null">#{maxUsers},</if>
            <if test="drawDelayMinutes != null">#{drawDelayMinutes},</if>
            <if test="drawTime != null">#{drawTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAutoRollConfig" parameterType="com.ruoyi.project.vimRoll.domain.AutoRollConfig">
        update sys_auto_roll_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="configName != null and configName != ''">config_name = #{configName},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="titleTemplate != null and titleTemplate != ''">title_template = #{titleTemplate},</if>
            <if test="infoTemplate != null">info_template = #{infoTemplate},</if>
            <if test="maxUsers != null">max_users = #{maxUsers},</if>
            <if test="drawDelayMinutes != null">draw_delay_minutes = #{drawDelayMinutes},</if>
            <if test="drawTime != null">draw_time = #{drawTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAutoRollConfigById" parameterType="Long">
        delete from sys_auto_roll_config where id = #{id}
    </delete>

    <delete id="deleteAutoRollConfigByIds" parameterType="String">
        delete from sys_auto_roll_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
