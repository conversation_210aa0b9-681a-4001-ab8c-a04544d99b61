<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimNotice.mapper.VimNoticeMapper">
    
    <resultMap type="VimNotice" id="VimNoticeResult">
        <result property="id"            column="id"            />
        <result property="notice"        column="notice"        />
        <result property="time"          column="time"          />
        <result property="sort"          column="sort"          /> 
    </resultMap>
    
    <sql id="selectVimNoticeVo">
        select id, notice, time,sort
        from vim_notice
    </sql>
    
    <select id="selectVimNoticeById" parameterType="Integer" resultMap="VimNoticeResult">
        <include refid="selectVimNoticeVo"/>
        where id = #{id}
    </select>
    
    <select id="selectVimNoticeList" parameterType="VimNotice" resultMap="VimNoticeResult">
        <include refid="selectVimNoticeVo"/>
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="notice != null and notice != ''">
                AND notice like concat('%', #{notice}, '%')
            </if>
            <if test="time != null">
                AND time = #{time}
            </if>
            <!-- 按照 sort排序 -->
            <if test="sort!= null">
                order by sort
            </if>
        </where>
    </select>
    
    <insert id="insertVimNotice" parameterType="VimNotice">
        insert into vim_notice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="notice != null and notice != ''">notice,</if>
            <if test="time != null">time,</if>
            <if test="sort != null and sort != ''">sort,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="notice != null and notice != ''">#{notice},</if>
            <if test="time != null">#{time},</if>
            <if test="sort != null and sort != ''">#{sort},</if>
        </trim>
    </insert>
    
    <update id="updateVimNotice" parameterType="VimNotice">
        update vim_notice
        <trim prefix="SET" suffixOverrides=",">
            <if test="notice != null and notice != ''">notice = #{notice},</if>
            <if test="time != null">time = #{time},</if>
            <if test="sort != null and sort != ''">sort = #{sort},</if>
        </trim>
        where id = #{id}
    </update>
    
    <delete id="deleteVimNoticeById" parameterType="Integer">
        delete from vim_notice where id = #{id}
    </delete>
    
    <delete id="deleteVimNoticeByIds" parameterType="Integer">
        delete from vim_notice where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>