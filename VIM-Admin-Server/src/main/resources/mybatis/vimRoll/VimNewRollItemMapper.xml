<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimRoll.mapper.VimNewRollItemMapper">
    
    <resultMap type="com.ruoyi.project.vimRoll.domain.VimNewRollItem" id="VimNewRollItemResult">
        <result property="id"    column="id"    />
        <result property="item"    column="item"    />
        <result property="itemLevel"    column="item_level"    />
        <result property="rollId"    column="roll_id"    />
        <result property="userId"    column="user_id"    />
        <result property="image"    column="image"    />
        <result property="itemName"    column="item_name"    />
        <result property="number"    column="number"    />
        <result property="totalCount"    column="total_count"    />
    </resultMap>

    <sql id="selectVimNewRollItemVo">
        select id, item, item_level, roll_id, user_id, image, item_name, number
        from vim_new_rollitem
    </sql>

    <select id="selectVimNewRollItemList" parameterType="com.ruoyi.project.vimRoll.domain.VimNewRollItem" resultMap="VimNewRollItemResult">
        <include refid="selectVimNewRollItemVo"/>
        <where>  
            <if test="item != null "> and item = #{item}</if>
            <if test="itemLevel != null "> and item_level = #{itemLevel}</if>
            <if test="rollId != null "> and roll_id = #{rollId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
        </where>
    </select>
    
    <select id="selectVimNewRollItemById" parameterType="Long" resultMap="VimNewRollItemResult">
        <include refid="selectVimNewRollItemVo"/>
        where id = #{id}
    </select>
    
    <select id="selectVimNewRollItemsByRollId" parameterType="Long" resultMap="VimNewRollItemResult">
        SELECT
            ANY_VALUE(id) AS id,
            item,
            ANY_VALUE(item_level) AS item_level,
            ANY_VALUE(roll_id) AS roll_id,
            ANY_VALUE(user_id) AS user_id,
            ANY_VALUE(image) AS image,
            ANY_VALUE(item_name) AS item_name,
            ANY_VALUE(number) AS number,
            COUNT(*) AS total_count
        FROM
            vim_new_rollitem
        WHERE
            roll_id = #{rollId}
        GROUP BY
            item
        ORDER BY
            item_level;
    </select>
        
    <insert id="insertVimNewRollItem" parameterType="com.ruoyi.project.vimRoll.domain.VimNewRollItem" useGeneratedKeys="true" keyProperty="id">
        insert into vim_new_rollitem
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="item != null">item,</if>
            <if test="itemLevel != null">item_level,</if>
            <if test="rollId != null">roll_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="image != null">image,</if>
            <if test="itemName != null">item_name,</if>
            <if test="number != null">number,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="item != null">#{item},</if>
            <if test="itemLevel != null">#{itemLevel},</if>
            <if test="rollId != null">#{rollId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="image != null">#{image},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="number != null">#{number},</if>
         </trim>
    </insert>

    <update id="updateVimNewRollItem" parameterType="com.ruoyi.project.vimRoll.domain.VimNewRollItem">
        update vim_new_rollitem
        <trim prefix="SET" suffixOverrides=",">
            <if test="item != null">item = #{item},</if>
            <if test="itemLevel != null">item_level = #{itemLevel},</if>
            <if test="rollId != null">roll_id = #{rollId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="image != null">image = #{image},</if>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="number != null">number = #{number},</if>
        </trim>
        where id = #{id}
    </update>
    
    <update id="updateVimNewRollItemWinner">
        update vim_new_rollitem set user_id = #{userId}, number = #{number}
        where id = #{id}
    </update>

    <delete id="deleteVimNewRollItemById" parameterType="Long">
        delete from vim_new_rollitem where id = #{id}
    </delete>

    <delete id="deleteVimNewRollItemByIds" parameterType="String">
        delete from vim_new_rollitem where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteVimNewRollItemByRollId" parameterType="Long">
        delete from vim_new_rollitem where roll_id = #{rollId}
    </delete>

    <select id="selectVimNewRollWinnersByRollId" parameterType="Long" resultMap="VimNewRollItemResult">
        SELECT
            i.id,
            i.item,
            i.item_level,
            i.roll_id,
            i.user_id,
            i.image,
            i.item_name,
            i.number
        FROM
            vim_new_rollitem i
        WHERE
            i.roll_id = #{rollId}
            AND i.user_id IS NOT NULL
        ORDER BY
            i.item_level, i.item
    </select>
</mapper> 