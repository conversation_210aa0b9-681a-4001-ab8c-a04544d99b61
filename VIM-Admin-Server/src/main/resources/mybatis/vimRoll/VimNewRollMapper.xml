<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimRoll.mapper.VimNewRollMapper">
    
    <resultMap type="com.ruoyi.project.vimRoll.domain.VimNewRoll" id="VimNewRollResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="info"    column="info"    />
        <result property="status"    column="status"    />
        <result property="userNow"    column="user_now"    />
        <result property="userMax"    column="user_max"    />
        <result property="endTime"    column="end_time"    />
        <result property="rollCreateTime"    column="create_time"    />
        <result property="rollUpdateTime"    column="update_time"    />
        <result property="seed"    column="seed"    />
    </resultMap>

    <sql id="selectVimNewRollVo">
        select id, title, info, status, user_now, user_max, end_time, create_time, update_time, seed
        from vim_new_roll
    </sql>

    <select id="selectVimNewRollList" parameterType="com.ruoyi.project.vimRoll.domain.VimNewRoll" resultMap="VimNewRollResult">
        <include refid="selectVimNewRollVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="userMax != null "> and user_max = #{userMax}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectVimNewRollById" parameterType="Long" resultMap="VimNewRollResult">
        <include refid="selectVimNewRollVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertVimNewRoll" parameterType="com.ruoyi.project.vimRoll.domain.VimNewRoll" useGeneratedKeys="true" keyProperty="id">
        insert into vim_new_roll
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="info != null">info,</if>
            <if test="status != null">status,</if>
            <if test="userNow != null">user_now,</if>
            <if test="userMax != null">user_max,</if>
            <if test="endTime != null">end_time,</if>
            <if test="rollCreateTime != null">create_time,</if>
            <if test="rollUpdateTime != null">update_time,</if>
            <if test="seed != null">seed,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="info != null">#{info},</if>
            <if test="status != null">#{status},</if>
            <if test="userNow != null">#{userNow},</if>
            <if test="userMax != null">#{userMax},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="rollCreateTime != null">#{rollCreateTime},</if>
            <if test="rollUpdateTime != null">#{rollUpdateTime},</if>
            <if test="seed != null">#{seed},</if>
         </trim>
    </insert>

    <update id="updateVimNewRoll" parameterType="com.ruoyi.project.vimRoll.domain.VimNewRoll">
        update vim_new_roll
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="info != null">info = #{info},</if>
            <if test="status != null">status = #{status},</if>
            <if test="userNow != null">user_now = #{userNow},</if>
            <if test="userMax != null">user_max = #{userMax},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="rollUpdateTime != null">update_time = #{rollUpdateTime},</if>
            <if test="seed != null">seed = #{seed},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateVimNewRollStatus">
        update vim_new_roll set status = #{status} where id = #{id}
    </update>
    
    <update id="updateVimNewRollUserNow">
        update vim_new_roll set user_now = #{userNow} where id = #{id}
    </update>

    <delete id="deleteVimNewRollById" parameterType="Long">
        delete from vim_new_roll where id = #{id}
    </delete>

    <delete id="deleteVimNewRollByIds" parameterType="String">
        delete from vim_new_roll where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 