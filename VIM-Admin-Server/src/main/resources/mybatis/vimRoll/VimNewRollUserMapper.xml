<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimRoll.mapper.VimNewRollUserMapper">
    
    <resultMap type="com.ruoyi.project.vimRoll.domain.VimNewRollUser" id="VimNewRollUserResult">
        <result property="id"    column="id"    />
        <result property="rollId"    column="roll_id"    />
        <result property="userId"    column="user_id"    />
        <result property="joinTime"    column="join_time"    />
    </resultMap>

    <sql id="selectVimNewRollUserVo">
        select id, roll_id, user_id, join_time
        from vim_new_rolluser
    </sql>

    <select id="selectVimNewRollUserList" parameterType="com.ruoyi.project.vimRoll.domain.VimNewRollUser" resultMap="VimNewRollUserResult">
        <include refid="selectVimNewRollUserVo"/>
        <where>  
            <if test="rollId != null "> and roll_id = #{rollId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
    </select>
    
    <select id="selectVimNewRollUserById" parameterType="Long" resultMap="VimNewRollUserResult">
        <include refid="selectVimNewRollUserVo"/>
        where id = #{id}
    </select>
    
    <select id="selectVimNewRollUsersByRollId" parameterType="Long" resultMap="VimNewRollUserResult">
        <include refid="selectVimNewRollUserVo"/>
        where roll_id = #{rollId}
        order by join_time desc
    </select>
    
    <select id="selectVimNewRollUserByRollIdAndUserId" resultMap="VimNewRollUserResult">
        <include refid="selectVimNewRollUserVo"/>
        where roll_id = #{rollId} and user_id = #{userId}
        limit 1
    </select>
    
    <select id="countVimNewRollUserByRollId" parameterType="Long" resultType="Integer">
        select count(1) from vim_new_rolluser where roll_id = #{rollId}
    </select>
        
    <insert id="insertVimNewRollUser" parameterType="com.ruoyi.project.vimRoll.domain.VimNewRollUser" useGeneratedKeys="true" keyProperty="id">
        insert into vim_new_rolluser
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rollId != null">roll_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="joinTime != null">join_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rollId != null">#{rollId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="joinTime != null">#{joinTime},</if>
         </trim>
    </insert>

    <update id="updateVimNewRollUser" parameterType="com.ruoyi.project.vimRoll.domain.VimNewRollUser">
        update vim_new_rolluser
        <trim prefix="SET" suffixOverrides=",">
            <if test="rollId != null">roll_id = #{rollId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="joinTime != null">join_time = #{joinTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimNewRollUserById" parameterType="Long">
        delete from vim_new_rolluser where id = #{id}
    </delete>

    <delete id="deleteVimNewRollUserByIds" parameterType="String">
        delete from vim_new_rolluser where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteVimNewRollUserByRollId" parameterType="Long">
        delete from vim_new_rolluser where roll_id = #{rollId}
    </delete>
</mapper> 