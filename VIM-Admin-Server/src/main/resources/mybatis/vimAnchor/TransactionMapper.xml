<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimAnchor.mapper.TransactionMapper">

    <!-- 交易记录VO结果映射 -->
    <resultMap id="TransactionVOMap" type="com.ruoyi.project.vimAnchor.domain.vo.TransactionVO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="username" property="username" />
        <result column="nickname" property="nickname" />
        <result column="type" property="type" />
        <result column="type_name" property="typeName" />
        <result column="amount" property="amount" />
        <result column="coins" property="coins" />
        <result column="status" property="status" />
        <result column="status_name" property="statusName" />
        <result column="create_time" property="createTime" />
    </resultMap>
    
    <!-- 充值记录VO结果映射 -->
    <resultMap id="RechargeRecordVOMap" type="com.ruoyi.project.vimAnchor.domain.vo.RechargeRecordVO">
        <id column="id" property="id" />
        <result column="amount" property="amount" />
        <result column="coins" property="coins" />
        <result column="status" property="status" />
        <result column="status_name" property="statusName" />
        <result column="create_time" property="createTime" />
    </resultMap>
    
    <!-- 消费记录VO结果映射 -->
    <resultMap id="ConsumeRecordVOMap" type="com.ruoyi.project.vimAnchor.domain.vo.ConsumeRecordVO">
        <id column="id" property="id" />
        <result column="amount" property="amount" />
        <result column="description" property="description" />
        <result column="balance" property="balance" />
        <result column="create_time" property="createTime" />
    </resultMap>
    
    <!-- 根据ID查询交易记录 -->
    <select id="selectTransactionById" resultMap="TransactionVOMap">
        SELECT 
            t.id,
            t.uid AS user_id,
            u.username,
            u.nickname,
            1 AS type,
            '充值' AS type_name,
            t.amount,
            t.coin AS coins,
            t.state AS status,
            CASE 
                WHEN t.state = 1 THEN '未支付'
                WHEN t.state = 2 THEN '已支付'
                WHEN t.state = 3 THEN '已支付但回调异常'
                ELSE '未知'
            END AS status_name,
            FROM_UNIXTIME(t.create_time, '%Y-%m-%d %H:%i:%s') AS create_time
        FROM 
            vim_order_recharge t
            LEFT JOIN vim_user u ON t.uid = u.id
        WHERE 
            t.id = #{id}
    </select>
    
    <!-- 查询交易记录列表 -->
    <select id="selectTransactionList" resultMap="TransactionVOMap">
        SELECT 
            t.id,
            t.uid AS user_id,
            u.username,
            u.nickname,
            1 AS type,
            '充值' AS type_name,
            t.amount,
            t.coin AS coins,
            t.state AS status,
            CASE 
                WHEN t.state = 0 THEN '处理中'
                WHEN t.state = 1 THEN '成功'
                WHEN t.state = 2 THEN '失败'
                ELSE '未知'
            END AS status_name,
            FROM_UNIXTIME(t.create_time, '%Y-%m-%d %H:%i:%s') AS create_time
        FROM 
            vim_order_recharge t
            LEFT JOIN vim_user u ON t.uid = u.id
        WHERE 
            1=1
            <if test="userId != null">
                AND t.uid = #{userId}
            </if>
            <if test="type != null">
                AND 1 = 1
            </if>
            <if test="status != null">
                AND t.state = #{status}
            </if>
            <if test="startTime != null">
                AND t.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND t.create_time &lt;= #{endTime}
            </if>
        ORDER BY t.create_time DESC
    </select>
    
    <!-- 查询交易记录总数 -->
    <select id="selectTransactionCount" resultType="int">
        SELECT 
            COUNT(*)
        FROM 
            vim_order_recharge t
        WHERE 
            1=1
            <if test="userId != null">
                AND t.uid = #{userId}
            </if>
            <if test="type != null">
                AND 1 = 1
            </if>
            <if test="status != null">
                AND t.state = #{status}
            </if>
            <if test="startTime != null">
                AND t.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND t.create_time &lt;= #{endTime}
            </if>
    </select>
    
    <!-- 查询用户充值记录 -->
    <select id="selectRechargeRecords" resultMap="RechargeRecordVOMap">
        SELECT 
            r.id,
            r.amount,
            r.coin AS coins,
            r.state AS status,
            CASE 
                WHEN r.state = 1 THEN '未支付'
                WHEN r.state = 2 THEN '已支付'
                WHEN r.state = 3 THEN '已支付但回调异常'
                ELSE '未知'
            END AS status_name,
            FROM_UNIXTIME(r.create_time, '%Y-%m-%d %H:%i:%s') AS create_time
        FROM 
            vim_order_recharge r
        WHERE 
            r.uid = #{userId}
        ORDER BY 
            r.create_time DESC
    </select>
    
    <!-- 查询用户充值记录总数 -->
    <select id="selectRechargeRecordsCount" resultType="int">
        SELECT 
            COUNT(*)
        FROM 
            vim_order_recharge r
        WHERE 
            r.uid = #{userId}
    </select>
    
    <!-- 查询用户消费记录 -->
    <select id="selectConsumeRecords" resultMap="ConsumeRecordVOMap">
        SELECT 
            p.id,
            p.amount,
            p.info as description,
            p.balance,
            FROM_UNIXTIME(p.time, '%Y-%m-%d %H:%i:%s') AS create_time
        FROM 
            vim_order_pay p
        WHERE 
            p.uid = #{userId}
        ORDER BY 
            p.time DESC
    </select>
    
    <!-- 查询用户消费记录总数 -->
    <select id="selectConsumeRecordsCount" resultType="int">
        SELECT 
            COUNT(*)
        FROM 
            vim_order_pay p
        WHERE 
            p.uid = #{userId}
    </select>
    
    <!-- 根据用户ID和时间范围查询充值总额 -->
    <select id="selectRechargeAmount" resultType="java.math.BigDecimal">
        SELECT 
            COALESCE(SUM(t.amount), 0)
        FROM 
            vim_order_recharge t
        WHERE 
            t.uid = #{userId}
            AND t.state = 1
            <if test="startTime != null">
                AND t.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND t.create_time &lt;= #{endTime}
            </if>
    </select>
    
    <!-- 根据用户ID和时间范围查询消费总额 -->
    <select id="selectConsumeAmount" resultType="java.math.BigDecimal">
        SELECT 
            COALESCE(SUM(p.amount), 0)
        FROM 
            vim_order_pay p
        WHERE 
            p.uid = #{userId}
            <if test="startTime != null">
                AND p.time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND p.time &lt;= #{endTime}
            </if>
    </select>
    
    <!-- 根据用户ID列表和时间范围查询充值总额 -->
    <select id="selectRechargeAmountByUserIds" resultType="java.math.BigDecimal">
        SELECT 
            COALESCE(SUM(t.amount), 0)
        FROM 
            vim_order_recharge t
        WHERE 
            t.uid IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
            AND t.state = 1
            <if test="startTime != null">
                AND t.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND t.create_time &lt;= #{endTime}
            </if>
    </select>
    
    <!-- 根据用户ID列表和时间范围查询消费总额 -->
    <select id="selectConsumeAmountByUserIds" resultType="java.math.BigDecimal">
        SELECT 
            COALESCE(SUM(p.amount), 0)
        FROM 
            vim_order_pay p
        WHERE 
            p.uid IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
            <if test="startTime != null">
                AND p.time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND p.time &lt;= #{endTime}
            </if>
    </select>
</mapper> 