<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimAnchor.mapper.AnchorMapper">

    <!-- 主播VO结果映射 -->
    <resultMap id="AnchorVOMap" type="com.ruoyi.project.vimAnchor.domain.vo.AnchorVO">
        <id column="id" property="id" />
        <result column="nickname" property="nickname" />
        <result column="avatar" property="avatar" />
        <result column="phone_number" property="phoneNumber" />
        <result column="user_count" property="userCount" />
        <result column="total_recharge" property="totalRecharge" />
        <result column="total_consume" property="totalConsume" />
        <result column="create_time" property="createTime" />

    </resultMap>
    
    <!-- 主播详情VO结果映射 -->
    <resultMap id="AnchorDetailVOMap" type="com.ruoyi.project.vimAnchor.domain.vo.AnchorDetailVO">
        <id column="id" property="id" />
        <result column="avatar" property="avatar" />
        <result column="nickname" property="nickname" />
        <result column="id_text" property="idText" />
        <result column="phone_number" property="phoneNumber" />
        <result column="invite_code" property="inviteCode" />
        <result column="create_time" property="createTime" />
        <result column="user_count" property="userCount" />
        <result column="total_recharge" property="totalRecharge" />
        <result column="total_consume" property="totalConsume" />

    </resultMap>
    
    <!-- 统计数据结果映射 -->
    <resultMap id="StatisticsVOMap" type="com.ruoyi.project.vimAnchor.domain.vo.StatisticsVO">
        <result column="total_recharge" property="totalRecharge" />
        <result column="total_consume" property="totalConsume" />
        <result column="user_count" property="userCount" />
        <result column="period_new_user_count" property="todayNewUserCount" />
        <result column="period_new_user_count" property="periodNewUserCount" />
        <result column="total_claim_amount" property="totalClaimAmount" />
        <result column="total_shipped_amount" property="totalShippedAmount" />
        <result column="total_backpack_amount" property="totalBackpackAmount" />
        <result column="total_subordinate_coin_balance" property="totalSubordinateCoinBalance" />
        <result column="total_subordinate_key_balance" property="totalSubordinateKeyBalance" />
        <result column="period_total_recharge" property="periodTotalRecharge" />
        <result column="period_actual_shipped_amount" property="periodActualShippedAmount" />
        <result column="period_expected_claim_amount" property="periodExpectedClaimAmount" />
        <!-- 🆕 新增：存储过程计算的利润相关字段 -->
        <result column="actual_profit" property="actualProfit" />
        <result column="profit_ratio" property="profitRatio" />
        <result column="total_turnover" property="totalTurnover" />
    </resultMap>

    <!-- 查询主播详情（带数据权限控制） -->
    <select id="selectAnchorDetail" resultMap="AnchorDetailVOMap">
        SELECT
            a.id,
            a.userimage AS avatar,
            a.nickname,
            CONCAT('ID: ', a.id) AS id_text,
            a.phone AS phone_number,
            a.invite_code,
            FROM_UNIXTIME(a.create_time, '%Y-%m-%d %H:%i:%s') AS create_time,
            (SELECT COUNT(*) FROM vim_user u WHERE u.invite_user = a.id) AS user_count,
            COALESCE(
                (SELECT SUM(t.amount) FROM vim_order_recharge t
                    INNER JOIN vim_user u ON t.uid = u.id
                    WHERE u.invite_user = a.id AND t.state = 2), 0
            ) AS total_recharge,
            COALESCE(
                (SELECT SUM(p.amount) FROM vim_order_pay p
                    INNER JOIN vim_user u ON p.uid = u.id
                    WHERE u.invite_user = a.id), 0
            ) AS total_consume
        FROM
            vim_user a
            <!-- 🔑 关键：通过手机号关联sys_user表实现数据权限控制 -->
            LEFT JOIN sys_user su ON a.phone = su.phonenumber AND su.del_flag = '0'
            LEFT JOIN sys_dept d ON su.dept_id = d.dept_id
        WHERE
            a.id = #{id}
            AND (a.identity = 2 OR a.identity = 3)
            <!-- 🔑 数据权限过滤：只允许查看权限范围内的主播详情 -->
            ${params.dataScope}
    </select>

    <!-- 查询主播详情（不带数据权限控制） -->
    <select id="selectAnchorDetailWithoutDataScope" resultMap="AnchorDetailVOMap">
        SELECT
            a.id,
            a.userimage AS avatar,
            a.nickname,
            CONCAT('ID: ', a.id) AS id_text,
            a.phone AS phone_number,
            a.invite_code,
            FROM_UNIXTIME(a.create_time, '%Y-%m-%d %H:%i:%s') AS create_time,
            (SELECT COUNT(*) FROM vim_user u WHERE u.invite_user = a.id) AS user_count,
            COALESCE(
                (SELECT SUM(t.amount) FROM vim_order_recharge t
                    INNER JOIN vim_user u ON t.uid = u.id
                    WHERE u.invite_user = a.id AND t.state = 2), 0
            ) AS total_recharge,
            COALESCE(
                (SELECT SUM(p.amount) FROM vim_order_pay p
                    INNER JOIN vim_user u ON p.uid = u.id
                    WHERE u.invite_user = a.id), 0
            ) AS total_consume
        FROM
            vim_user a
        WHERE
            a.id = #{id}
            AND (a.identity = 2 OR a.identity = 3)
    </select>
    
    <!-- 根据ID查询主播 -->
    <select id="selectAnchorById" resultMap="AnchorVOMap">
        SELECT 
            a.id,
            a.username,
            a.nickname,
            a.userimage AS avatar,
            1 AS status,
            (SELECT COUNT(*) FROM vim_user u WHERE u.invite_user = a.id) AS user_count,
            COALESCE(
                (SELECT SUM(t.amount) FROM vim_order_recharge t 
                    INNER JOIN vim_user u ON t.uid = u.id 
                    WHERE u.invite_user = a.id AND t.state = 2), 0
            ) AS total_recharge,
            COALESCE(
                (SELECT SUM(p.amount) FROM vim_order_pay p 
                    INNER JOIN vim_user u ON p.uid = u.id 
                    WHERE u.invite_user = a.id), 0
            ) AS total_consume,
            FROM_UNIXTIME(a.create_time, '%Y-%m-%d %H:%i:%s') AS create_time
        FROM 
            vim_user a
        WHERE
            a.id = #{id}
            AND (a.identity = 2 OR a.identity = 3)
    </select>
    
    <!-- 查询主播列表（带数据权限控制） -->
    <select id="selectAnchorList" parameterType="com.ruoyi.project.vimAnchor.domain.dto.AnchorQueryDTO" resultMap="AnchorVOMap">
        SELECT
            a.id,
            a.nickname,
            a.userimage AS avatar,
            a.phone AS phone_number,
            (SELECT COUNT(*) FROM vim_user u WHERE u.invite_user = a.id) AS user_count,
            COALESCE(
                (SELECT SUM(t.amount) FROM vim_order_recharge t
                    INNER JOIN vim_user u ON t.uid = u.id
                    WHERE u.invite_user = a.id AND t.state = 2), 0
            ) AS total_recharge,
            COALESCE(
                (SELECT SUM(p.amount) FROM vim_order_pay p
                    INNER JOIN vim_user u ON p.uid = u.id
                    WHERE u.invite_user = a.id), 0
            ) AS total_consume,
            FROM_UNIXTIME(a.create_time, '%Y-%m-%d %H:%i:%s') AS create_time
        FROM
            vim_user a
            <!-- 🔑 关键：通过手机号关联sys_user表实现数据权限控制 -->
            LEFT JOIN sys_user su ON a.phone = su.phonenumber AND su.del_flag = '0'
            LEFT JOIN sys_dept d ON su.dept_id = d.dept_id
        WHERE
            (a.identity = 2 OR a.identity = 3)
            <if test="nickname != null and nickname != ''">
                AND a.nickname LIKE CONCAT('%', #{nickname}, '%')
            </if>
            <if test="startDate != null and startDate != ''">
                AND FROM_UNIXTIME(a.create_time, '%Y-%m-%d') &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND FROM_UNIXTIME(a.create_time, '%Y-%m-%d') &lt;= #{endDate}
            </if>
            <!-- 🔑 数据权限过滤：只显示当前用户权限范围内的主播 -->
            ${params.dataScope}
        ORDER BY a.create_time DESC
        <if test="pageSize != null">
            LIMIT #{pageSize}
        </if>
        <if test="offset != null">
            OFFSET #{offset}
        </if>
    </select>
    
    <!-- 查询主播总数（带数据权限控制） -->
    <select id="selectAnchorCount" parameterType="com.ruoyi.project.vimAnchor.domain.dto.AnchorQueryDTO" resultType="int">
        SELECT
            COUNT(*)
        FROM
            vim_user a
            <!-- 🔑 关键：通过手机号关联sys_user表实现数据权限控制 -->
            LEFT JOIN sys_user su ON a.phone = su.phonenumber AND su.del_flag = '0'
            LEFT JOIN sys_dept d ON su.dept_id = d.dept_id
        WHERE
            (a.identity = 2 OR a.identity = 3)
            <if test="nickname != null and nickname != ''">
                AND a.nickname LIKE CONCAT('%', #{nickname}, '%')
            </if>
            <if test="startDate != null and startDate != ''">
                AND FROM_UNIXTIME(a.create_time, '%Y-%m-%d') &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND FROM_UNIXTIME(a.create_time, '%Y-%m-%d') &lt;= #{endDate}
            </if>
            <!-- 🔑 数据权限过滤：只统计当前用户权限范围内的主播 -->
            ${params.dataScope}
    </select>
    
    <!-- 查询平台统计数据 -->
    <select id="selectPlatformStatistics" resultMap="StatisticsVOMap">
        SELECT
            COALESCE((
                SELECT SUM(t.amount) FROM vim_order_recharge t
                WHERE t.state = 1
                AND t.create_time &gt;= UNIX_TIMESTAMP(CURDATE())
            ), 0) AS today_recharge,
            
            COALESCE((
                SELECT SUM(p.amount) FROM vim_order_pay p
                WHERE p.time &gt;= UNIX_TIMESTAMP(CURDATE())
            ), 0) AS today_consume,
            
            COALESCE((
                SELECT SUM(t.amount) FROM vim_order_recharge t
                WHERE t.state = 1
                AND t.create_time &gt;= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL 1 DAY))
                AND t.create_time &lt; UNIX_TIMESTAMP(CURDATE())
            ), 0) AS yesterday_recharge,
            
            COALESCE((
                SELECT SUM(p.amount) FROM vim_order_pay p
                WHERE p.time &gt;= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL 1 DAY))
                AND p.time &lt; UNIX_TIMESTAMP(CURDATE())
            ), 0) AS yesterday_consume,
            
            COALESCE((
                SELECT SUM(t.amount) FROM vim_order_recharge t
                WHERE t.state = 1
                AND t.create_time &gt;= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY))
            ), 0) AS this_week_recharge,
            
            COALESCE((
                SELECT SUM(p.amount) FROM vim_order_pay p
                WHERE p.time &gt;= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY))
            ), 0) AS this_week_consume,
            
            COALESCE((
                SELECT SUM(t.amount) FROM vim_order_recharge t
                WHERE t.state = 1
                AND t.create_time &gt;= UNIX_TIMESTAMP(DATE_FORMAT(CURDATE(), '%Y-%m-01'))
            ), 0) AS this_month_recharge,
            
            COALESCE((
                SELECT SUM(p.amount) FROM vim_order_pay p
                WHERE p.time &gt;= UNIX_TIMESTAMP(DATE_FORMAT(CURDATE(), '%Y-%m-01'))
            ), 0) AS this_month_consume,
            
            COALESCE((
                SELECT SUM(t.amount) FROM vim_order_recharge t
                WHERE t.state = 1
            ), 0) AS total_recharge,
            
            COALESCE((
                SELECT SUM(p.amount) FROM vim_order_pay p
            ), 0) AS total_consume,
            
            (SELECT COUNT(*) FROM vim_user) AS user_count,
            
            (SELECT COUNT(*) FROM vim_user 
             WHERE create_time &gt;= UNIX_TIMESTAMP(CURDATE())) AS today_new_user_count
    </select>
    
    <!-- 查询主播统计数据 -->
    <select id="selectAnchorStatistics" resultMap="StatisticsVOMap" statementType="CALLABLE">
        {CALL GetInviteUserStatistics(
            #{anchorId},
            #{startTime, jdbcType=BIGINT},
            #{endTime, jdbcType=BIGINT}
        )}
    </select>

    <!-- 查询小时趋势数据 -->
    <select id="selectHourlyTrendData" resultType="java.util.Map">
        SELECT
            /* 充值数据 */
            COALESCE((
                SELECT SUM(r.amount) FROM vim_order_recharge r
                INNER JOIN vim_user u ON r.uid = u.id
                WHERE u.invite_user = #{anchorId}
                AND r.state = 2
                AND r.create_time >= UNIX_TIMESTAMP(DATE_FORMAT(#{time}, '%Y-%m-%d %H:00:00'))
                AND r.create_time &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{time} + INTERVAL 1 HOUR, '%Y-%m-%d %H:00:00'))
            ), 0) AS rechargeAmount,

            COALESCE((
                SELECT COUNT(*) FROM vim_order_recharge r
                INNER JOIN vim_user u ON r.uid = u.id
                WHERE u.invite_user = #{anchorId}
                AND r.state = 2
                AND r.create_time >= UNIX_TIMESTAMP(DATE_FORMAT(#{time}, '%Y-%m-%d %H:00:00'))
                AND r.create_time &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{time} + INTERVAL 1 HOUR, '%Y-%m-%d %H:00:00'))
            ), 0) AS rechargeCount,

            /* 消费数据 */
            COALESCE((
                SELECT SUM(p.amount) FROM vim_order_pay p
                INNER JOIN vim_user u ON p.uid = u.id
                WHERE u.invite_user = #{anchorId}
                AND p.time >= UNIX_TIMESTAMP(DATE_FORMAT(#{time}, '%Y-%m-%d %H:00:00'))
                AND p.time &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{time} + INTERVAL 1 HOUR, '%Y-%m-%d %H:00:00'))
            ), 0) AS consumeAmount,

            COALESCE((
                SELECT COUNT(*) FROM vim_order_pay p
                INNER JOIN vim_user u ON p.uid = u.id
                WHERE u.invite_user = #{anchorId}
                AND p.time >= UNIX_TIMESTAMP(DATE_FORMAT(#{time}, '%Y-%m-%d %H:00:00'))
                AND p.time &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{time} + INTERVAL 1 HOUR, '%Y-%m-%d %H:00:00'))
            ), 0) AS consumeCount,

            /* 开箱数据 */
            COALESCE((
                SELECT SUM(amount) FROM (
                    SELECT k.amount FROM vim_order_key k
                    INNER JOIN vim_user u ON k.uid = u.id
                    WHERE u.invite_user = #{anchorId}
                    AND k.time >= UNIX_TIMESTAMP(DATE_FORMAT(#{time}, '%Y-%m-%d %H:00:00'))
                    AND k.time &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{time} + INTERVAL 1 HOUR, '%Y-%m-%d %H:00:00'))
                    UNION ALL
                    SELECT b.price AS amount FROM vim_order_box b
                    INNER JOIN vim_user u ON b.uid = u.id
                    WHERE u.invite_user = #{anchorId}
                    AND CAST(b.timestamp AS UNSIGNED) / 1000 >= UNIX_TIMESTAMP(DATE_FORMAT(#{time}, '%Y-%m-%d %H:00:00'))
                    AND CAST(b.timestamp AS UNSIGNED) / 1000 &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{time} + INTERVAL 1 HOUR, '%Y-%m-%d %H:00:00'))
                ) AS combined_amount
            ), 0) AS boxAmount,

            COALESCE((
                SELECT COUNT(*) FROM (
                    SELECT 1 FROM vim_order_key k
                    INNER JOIN vim_user u ON k.uid = u.id
                    WHERE u.invite_user = #{anchorId}
                    AND k.time >= UNIX_TIMESTAMP(DATE_FORMAT(#{time}, '%Y-%m-%d %H:00:00'))
                    AND k.time &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{time} + INTERVAL 1 HOUR, '%Y-%m-%d %H:00:00'))
                    UNION ALL
                    SELECT 1 FROM vim_order_box b
                    INNER JOIN vim_user u ON b.uid = u.id
                    WHERE u.invite_user = #{anchorId}
                    AND CAST(b.timestamp AS UNSIGNED) / 1000 >= UNIX_TIMESTAMP(DATE_FORMAT(#{time}, '%Y-%m-%d %H:00:00'))
                    AND CAST(b.timestamp AS UNSIGNED) / 1000 &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{time} + INTERVAL 1 HOUR, '%Y-%m-%d %H:00:00'))
                ) AS combined_count
            ), 0) AS boxCount
    </select>

    <!-- 查询日趋势数据 -->
    <select id="selectDailyTrendData" resultType="java.util.Map">
        SELECT
            /* 充值数据 */
            COALESCE((
                SELECT SUM(r.amount) FROM vim_order_recharge r
                INNER JOIN vim_user u ON r.uid = u.id
                WHERE u.invite_user = #{anchorId}
                AND r.state = 2
                AND r.create_time >= UNIX_TIMESTAMP(#{date})
                AND r.create_time &lt; UNIX_TIMESTAMP(#{date} + INTERVAL 1 DAY)
            ), 0) AS rechargeAmount,

            COALESCE((
                SELECT COUNT(*) FROM vim_order_recharge r
                INNER JOIN vim_user u ON r.uid = u.id
                WHERE u.invite_user = #{anchorId}
                AND r.state = 2
                AND r.create_time >= UNIX_TIMESTAMP(#{date})
                AND r.create_time &lt; UNIX_TIMESTAMP(#{date} + INTERVAL 1 DAY)
            ), 0) AS rechargeCount,

            /* 消费数据 */
            COALESCE((
                SELECT SUM(p.amount) FROM vim_order_pay p
                INNER JOIN vim_user u ON p.uid = u.id
                WHERE u.invite_user = #{anchorId}
                AND p.time >= UNIX_TIMESTAMP(#{date})
                AND p.time &lt; UNIX_TIMESTAMP(#{date} + INTERVAL 1 DAY)
            ), 0) AS consumeAmount,

            COALESCE((
                SELECT COUNT(*) FROM vim_order_pay p
                INNER JOIN vim_user u ON p.uid = u.id
                WHERE u.invite_user = #{anchorId}
                AND p.time >= UNIX_TIMESTAMP(#{date})
                AND p.time &lt; UNIX_TIMESTAMP(#{date} + INTERVAL 1 DAY)
            ), 0) AS consumeCount,

            /* 开箱数据 */
            COALESCE((
                SELECT SUM(amount) FROM (
                    SELECT k.amount FROM vim_order_key k
                    INNER JOIN vim_user u ON k.uid = u.id
                    WHERE u.invite_user = #{anchorId}
                    AND k.time >= UNIX_TIMESTAMP(#{date})
                    AND k.time &lt; UNIX_TIMESTAMP(#{date} + INTERVAL 1 DAY)
                    UNION ALL
                    SELECT b.price AS amount FROM vim_order_box b
                    INNER JOIN vim_user u ON b.uid = u.id
                    WHERE u.invite_user = #{anchorId}
                    AND CAST(b.timestamp AS UNSIGNED) / 1000 >= UNIX_TIMESTAMP(#{date})
                    AND CAST(b.timestamp AS UNSIGNED) / 1000 &lt; UNIX_TIMESTAMP(#{date} + INTERVAL 1 DAY)
                ) AS combined_amount
            ), 0) AS boxAmount,

            COALESCE((
                SELECT COUNT(*) FROM (
                    SELECT 1 FROM vim_order_key k
                    INNER JOIN vim_user u ON k.uid = u.id
                    WHERE u.invite_user = #{anchorId}
                    AND k.time >= UNIX_TIMESTAMP(#{date})
                    AND k.time &lt; UNIX_TIMESTAMP(#{date} + INTERVAL 1 DAY)
                    UNION ALL
                    SELECT 1 FROM vim_order_box b
                    INNER JOIN vim_user u ON b.uid = u.id
                    WHERE u.invite_user = #{anchorId}
                    AND CAST(b.timestamp AS UNSIGNED) / 1000 >= UNIX_TIMESTAMP(#{date})
                    AND CAST(b.timestamp AS UNSIGNED) / 1000 &lt; UNIX_TIMESTAMP(#{date} + INTERVAL 1 DAY)
                ) AS combined_count
            ), 0) AS boxCount
    </select>

    <!-- 查询月趋势数据 -->
    <select id="selectMonthlyTrendData" resultType="java.util.Map">
        SELECT
            /* 充值数据 */
            COALESCE((
                SELECT SUM(r.amount) FROM vim_order_recharge r
                INNER JOIN vim_user u ON r.uid = u.id
                WHERE u.invite_user = #{anchorId}
                AND r.state = 2
                AND r.create_time >= UNIX_TIMESTAMP(DATE_FORMAT(#{date}, '%Y-%m-01'))
                AND r.create_time &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{date} + INTERVAL 1 MONTH, '%Y-%m-01'))
            ), 0) AS rechargeAmount,

            COALESCE((
                SELECT COUNT(*) FROM vim_order_recharge r
                INNER JOIN vim_user u ON r.uid = u.id
                WHERE u.invite_user = #{anchorId}
                AND r.state = 2
                AND r.create_time >= UNIX_TIMESTAMP(DATE_FORMAT(#{date}, '%Y-%m-01'))
                AND r.create_time &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{date} + INTERVAL 1 MONTH, '%Y-%m-01'))
            ), 0) AS rechargeCount,

            /* 消费数据 */
            COALESCE((
                SELECT SUM(p.amount) FROM vim_order_pay p
                INNER JOIN vim_user u ON p.uid = u.id
                WHERE u.invite_user = #{anchorId}
                AND p.time >= UNIX_TIMESTAMP(DATE_FORMAT(#{date}, '%Y-%m-01'))
                AND p.time &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{date} + INTERVAL 1 MONTH, '%Y-%m-01'))
            ), 0) AS consumeAmount,

            COALESCE((
                SELECT COUNT(*) FROM vim_order_pay p
                INNER JOIN vim_user u ON p.uid = u.id
                WHERE u.invite_user = #{anchorId}
                AND p.time >= UNIX_TIMESTAMP(DATE_FORMAT(#{date}, '%Y-%m-01'))
                AND p.time &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{date} + INTERVAL 1 MONTH, '%Y-%m-01'))
            ), 0) AS consumeCount,

            /* 开箱数据 */
            COALESCE((
                SELECT SUM(amount) FROM (
                    SELECT k.amount FROM vim_order_key k
                    INNER JOIN vim_user u ON k.uid = u.id
                    WHERE u.invite_user = #{anchorId}
                    AND k.time >= UNIX_TIMESTAMP(DATE_FORMAT(#{date}, '%Y-%m-01'))
                    AND k.time &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{date} + INTERVAL 1 MONTH, '%Y-%m-01'))
                    UNION ALL
                    SELECT b.price AS amount FROM vim_order_box b
                    INNER JOIN vim_user u ON b.uid = u.id
                    WHERE u.invite_user = #{anchorId}
                    AND CAST(b.timestamp AS UNSIGNED) / 1000 >= UNIX_TIMESTAMP(DATE_FORMAT(#{date}, '%Y-%m-01'))
                    AND CAST(b.timestamp AS UNSIGNED) / 1000 &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{date} + INTERVAL 1 MONTH, '%Y-%m-01'))
                ) AS combined_amount
            ), 0) AS boxAmount,

            COALESCE((
                SELECT COUNT(*) FROM (
                    SELECT 1 FROM vim_order_key k
                    INNER JOIN vim_user u ON k.uid = u.id
                    WHERE u.invite_user = #{anchorId}
                    AND k.time >= UNIX_TIMESTAMP(DATE_FORMAT(#{date}, '%Y-%m-01'))
                    AND k.time &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{date} + INTERVAL 1 MONTH, '%Y-%m-01'))
                    UNION ALL
                    SELECT 1 FROM vim_order_box b
                    INNER JOIN vim_user u ON b.uid = u.id
                    WHERE u.invite_user = #{anchorId}
                    AND CAST(b.timestamp AS UNSIGNED) / 1000 >= UNIX_TIMESTAMP(DATE_FORMAT(#{date}, '%Y-%m-01'))
                    AND CAST(b.timestamp AS UNSIGNED) / 1000 &lt; UNIX_TIMESTAMP(DATE_FORMAT(#{date} + INTERVAL 1 MONTH, '%Y-%m-01'))
                ) AS combined_count
            ), 0) AS boxCount
    </select>

</mapper>