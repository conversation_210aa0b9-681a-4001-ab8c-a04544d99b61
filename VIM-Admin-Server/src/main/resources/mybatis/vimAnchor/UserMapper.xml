<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.vimAnchor.mapper.UserMapper">

    <!-- 用户VO结果映射 -->
    <resultMap id="UserVOMap" type="com.ruoyi.project.vimAnchor.domain.vo.UserVO">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="nickname" property="nickname" />
        <result column="avatar" property="avatar" />
        <result column="invite_user" property="inviteUser" />
        <result column="invite_user_name" property="inviteUserName" />
        <result column="status" property="status" />
        <result column="total_recharge" property="totalRecharge" />
        <result column="total_consume" property="totalConsume" />
        <result column="balance" property="balance" />
        <result column="last_recharge_time" property="lastRechargeTime" />
        <result column="last_consume_time" property="lastConsumeTime" />
        <result column="create_time" property="createTime" />
        <result column="first_recharge_amount" property="firstRechargeAmount" />
        <result column="first_recharge_time" property="firstRechargeTime" />
    </resultMap>
    
    <!-- 用户详情VO结果映射 -->
    <resultMap id="UserDetailVOMap" type="com.ruoyi.project.vimAnchor.domain.vo.UserDetailVO">
        <id column="id" property="id" />
        <result column="nickname" property="nickname" />
        <result column="username" property="username" />
        <result column="level" property="level" />
        <result column="create_time" property="createTime" />
        <result column="last_login_time" property="lastLoginTime" />
    </resultMap>


    
    <!-- 根据ID查询用户 -->
    <select id="selectUserById" resultMap="UserVOMap">
        SELECT 
            u.id,
            u.username,
            u.nickname,
            u.userimage AS avatar,
            u.invite_user,
            (SELECT a.nickname FROM vim_user a WHERE a.id = u.invite_user) AS invite_user_name,
            1 AS status,
            COALESCE((
                SELECT SUM(t.amount) FROM vim_order_recharge t
                WHERE t.uid = u.id AND t.state = 2
            ), 0) AS total_recharge,
            COALESCE((
                SELECT SUM(p.amount) FROM vim_order_pay p 
                WHERE p.uid = u.id
            ), 0) AS total_consume,
            (COALESCE((
                SELECT SUM(t.amount) FROM vim_order_recharge t
                WHERE t.uid = u.id AND t.state = 2
            ), 0) - COALESCE((
                SELECT SUM(p.amount) FROM vim_order_pay p 
                WHERE p.uid = u.id
            ), 0)) AS balance,
            (SELECT FROM_UNIXTIME(MAX(t.create_time), '%Y-%m-%d %H:%i:%s') FROM vim_order_recharge t 
                WHERE t.uid = u.id AND t.state = 1) AS last_recharge_time,
            (SELECT FROM_UNIXTIME(MAX(p.time), '%Y-%m-%d %H:%i:%s') FROM vim_order_pay p 
                WHERE p.uid = u.id) AS last_consume_time,
            FROM_UNIXTIME(u.create_time, '%Y-%m-%d %H:%i:%s') AS create_time
        FROM 
            vim_user u
        WHERE 
            u.id = #{id}
    </select>
    
    <!-- 查询用户详情 -->
    <select id="selectUserDetail" resultMap="UserDetailVOMap">
        SELECT 
            u.id,
            u.nickname,
            u.username,
            u.exp AS experience,
            u.level,
            FROM_UNIXTIME(u.create_time, '%Y-%m-%d %H:%i:%s') AS create_time,
            FROM_UNIXTIME(u.last_login_time, '%Y-%m-%d %H:%i:%s') AS last_login_time
        FROM 
            vim_user u
        WHERE 
            u.id = #{id}
    </select>
    
    <!-- 查询用户列表 -->
    <select id="selectUserList" resultMap="UserVOMap">
        SELECT 
            u.id,
            u.username,
            u.nickname,
            u.userimage AS avatar,
            u.invite_user,
            (SELECT a.nickname FROM vim_user a WHERE a.id = u.invite_user) AS invite_user_name,
            1 AS status,
            COALESCE((
                SELECT SUM(t.amount) FROM vim_order_recharge t 
                WHERE t.uid = u.id AND t.state = 2
            ), 0) AS total_recharge,
        COALESCE((SELECT SUM(ABS(p.amount)) FROM vim_order_pay p WHERE p.uid = u.id AND p.amount &lt; 0), 0) AS total_consume,
            (COALESCE((
                SELECT SUM(t.amount) FROM vim_order_recharge t 
                WHERE t.uid = u.id AND t.state = 1
            ), 0) - COALESCE((
                SELECT SUM(p.amount) FROM vim_order_pay p 
                WHERE p.uid = u.id
            ), 0)) AS balance,
            (SELECT FROM_UNIXTIME(MAX(t.create_time), '%Y-%m-%d %H:%i:%s') FROM vim_order_recharge t 
                WHERE t.uid = u.id AND t.state = 1) AS last_recharge_time,
            (SELECT FROM_UNIXTIME(MAX(p.time), '%Y-%m-%d %H:%i:%s') FROM vim_order_pay p 
                WHERE p.uid = u.id) AS last_consume_time,
            FROM_UNIXTIME(u.create_time, '%Y-%m-%d %H:%i:%s') AS create_time
        FROM 
            vim_user u
        WHERE 
            1=1
            <if test="anchorId != null">
                AND u.invite_user = #{anchorId}
            </if>
            <if test="name != null and name != ''">
                AND (u.username LIKE CONCAT('%', #{name}, '%') OR u.nickname LIKE CONCAT('%', #{name}, '%'))
            </if>
            <if test="status != null">
                AND 1=1
            </if>
        ORDER BY u.create_time DESC
    </select>
    
    <!-- 按照主播ID查询用户列表（带日期筛选和数据权限控制） -->
    <select id="selectUserListByAnchor" parameterType="com.ruoyi.project.vimAnchor.domain.dto.UserQueryDTO" resultMap="UserVOMap">
        SELECT
        u.id,
        u.userimage AS avatar,
        u.nickname,
        COALESCE((
        SELECT SUM(t.amount) FROM vim_order_recharge t
        WHERE t.uid = u.id AND t.state = 2
        ), 0) AS total_recharge,
        COALESCE((SELECT SUM(ABS(p.amount)) FROM vim_order_pay p WHERE p.uid = u.id AND p.amount &lt; 0), 0) AS
        total_consume,
        FROM_UNIXTIME(u.create_time, '%Y-%m-%d %H:%i:%s') AS create_time,
        <!-- 🔑 首次充值信息：LEFT JOIN获取首次充值记录 -->
        first_recharge.amount AS first_recharge_amount,
        FROM_UNIXTIME(first_recharge.create_time, '%Y-%m-%d') AS first_recharge_time
        FROM
        vim_user u
        <!-- 🔑 关键：通过主播手机号关联sys_user表实现数据权限控制 -->
        LEFT JOIN vim_user anchor ON u.invite_user = anchor.id
        LEFT JOIN sys_user su ON anchor.phone = su.phonenumber AND su.del_flag = '0'
        LEFT JOIN sys_dept d ON su.dept_id = d.dept_id
        <!-- 🔑 LEFT JOIN首次充值记录：获取每个用户的第一条充值记录 -->
        LEFT JOIN (
            SELECT
                r1.uid,
                r1.amount,
                r1.create_time
            FROM vim_order_recharge r1
            INNER JOIN (
                SELECT uid, MIN(create_time) as first_time
                FROM vim_order_recharge
                WHERE state = 2  -- 已支付状态
                GROUP BY uid
            ) r2 ON r1.uid = r2.uid AND r1.create_time = r2.first_time AND r1.state = 2
        ) first_recharge ON u.id = first_recharge.uid
        WHERE
        u.invite_user = #{anchorId}
        <if test="nickname != null and nickname != ''">
            AND u.nickname LIKE CONCAT('%', #{nickname}, '%')
        </if>
        <!-- 🔧 移除时间过滤条件：查看下级用户应显示所有历史用户，确保与总数统计一致 -->
        <!-- 🔑 数据权限过滤：只显示当前用户权限范围内的主播的下级用户 -->
        ${params.dataScope}
        ORDER BY u.create_time DESC
    </select>
    
    <!-- 查询用户总数 -->
    <select id="selectUserCount" resultType="int">
        SELECT 
            COUNT(*)
        FROM 
            vim_user u
        WHERE 
            1=1
            <if test="anchorId != null">
                AND u.invite_user = #{anchorId}
            </if>
            <if test="name != null and name != ''">
                AND (u.username LIKE CONCAT('%', #{name}, '%') OR u.nickname LIKE CONCAT('%', #{name}, '%'))
            </if>
            <if test="status != null">
                AND 1=1
            </if>
    </select>
    
    <!-- 按照主播ID查询用户总数（带数据权限控制） -->
    <select id="selectUserCountByAnchor" parameterType="com.ruoyi.project.vimAnchor.domain.dto.UserQueryDTO" resultType="int">
        SELECT
            COUNT(*)
        FROM
            vim_user u
            <!-- 🔑 关键：通过主播手机号关联sys_user表实现数据权限控制 -->
            LEFT JOIN vim_user anchor ON u.invite_user = anchor.id
            LEFT JOIN sys_user su ON anchor.phone = su.phonenumber AND su.del_flag = '0'
            LEFT JOIN sys_dept d ON su.dept_id = d.dept_id
        WHERE
            u.invite_user = #{anchorId}
            <if test="nickname != null and nickname != ''">
                AND u.nickname LIKE CONCAT('%', #{nickname}, '%')
            </if>
            <!-- 🔧 移除时间过滤条件：与用户列表查询保持一致，统计所有历史用户 -->
            <!-- 🔑 数据权限过滤：只统计当前用户权限范围内的主播的下级用户 -->
            ${params.dataScope}
    </select>
    
    <!-- 根据主播ID查询所有用户ID -->
    <select id="selectUserIdsByAnchorId" resultType="int">
        SELECT 
            u.id
        FROM 
            vim_user u
        WHERE 
            u.invite_user = #{anchorId}
    </select>
    
    <!-- 查询主播下的用户数量 -->
    <select id="selectUserCountByAnchorId" resultType="int">
        SELECT
            COUNT(*)
        FROM
            vim_user u
        WHERE
            u.invite_user = #{anchorId}
    </select>


</mapper>