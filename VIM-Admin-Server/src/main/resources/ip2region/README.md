# IP2Region数据库文件

## 文件说明

本目录用于存放ip2region数据库文件，用于IP地址归属地查询。

## 文件要求

- 文件名：`ip2region.xdb`
- 格式：ip2region xdb格式的二进制文件
- 大小：约10-15MB

## 获取数据库文件

### 方式一：从官方仓库下载
```bash
# 下载最新版本的xdb文件
wget https://github.com/lionsoul2014/ip2region/raw/master/data/ip2region.xdb
```

### 方式二：从CDN下载
```bash
# 使用CDN镜像下载
wget https://cdn.jsdelivr.net/gh/lionsoul2014/ip2region@master/data/ip2region.xdb
```

### 方式三：手动下载
1. 访问 https://github.com/lionsoul2014/ip2region
2. 进入 `data` 目录
3. 下载 `ip2region.xdb` 文件
4. 将文件放置到本目录下

## 文件更新

数据库文件建议定期更新以保证IP地址归属地信息的准确性：

- 更新频率：建议每月更新一次
- 更新方式：可以通过自动化脚本定期下载最新版本
- 热更新：系统支持在运行时更新数据库文件

## 注意事项

1. **文件大小**：确保下载的文件大小在10-15MB范围内
2. **文件完整性**：建议验证文件的MD5或SHA1校验和
3. **权限设置**：确保应用程序有读取该文件的权限
4. **备份策略**：建议保留旧版本文件作为备份

## 数据格式

ip2region.xdb文件包含以下信息：
- 国家
- 区域/大区
- 省份
- 城市
- ISP运营商

查询结果格式：`国家|区域|省份|城市|ISP`

## 性能特点

- **查询速度**：单次查询 < 1ms
- **内存占用**：约50MB（内存映射）
- **并发支持**：支持高并发查询
- **准确率**：>95%

## 故障排除

### 常见问题

1. **文件不存在**
   - 检查文件路径是否正确
   - 确认文件已正确下载

2. **文件损坏**
   - 重新下载文件
   - 验证文件完整性

3. **权限问题**
   - 检查文件读取权限
   - 确认应用程序运行用户权限

4. **内存不足**
   - 检查系统可用内存
   - 调整JVM内存参数

### 日志查看

查看应用程序日志中的IP2Region相关信息：
```
grep "IP2Region" application.log
```

## 版本信息

- 当前支持版本：ip2region v2.x
- 数据库格式：xdb (极速版)
- 兼容性：向后兼容

## 联系方式

如有问题，请联系开发团队或查看项目文档。
