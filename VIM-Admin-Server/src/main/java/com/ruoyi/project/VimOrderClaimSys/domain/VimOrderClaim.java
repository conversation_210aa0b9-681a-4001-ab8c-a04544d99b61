package com.ruoyi.project.VimOrderClaimSys.domain;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;

/**
 * 订单发货对象 vim_order_claim
 * 
 * <AUTHOR> and 羊
 * @date 2025-04-16
 */
@Setter
@Getter
@ApiModel(value = "VimOrderClaim", description = "订单发货信息对象")
public class VimOrderClaim extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 提货订单id */
    @ApiModelProperty(value = "提货订单ID", example = "ORDER123456")
    @Excel(name = "提货订单id")
    private String id;

    /** 对应的物品订单id */
    @ApiModelProperty(value = "对应的物品订单ID", example = "BOX123456")
    @Excel(name = "对应的物品订单id")
    private String oid;

    /** 用户id */
    @ApiModelProperty(value = "用户ID", example = "1001")
    @Excel(name = "用户id")
    private Long uid;

    /** 商品id */
    @ApiModelProperty(value = "商品ID", example = "101")
    @Excel(name = "商品id")
    private Long itemid;

    /** 商品名 */
    @ApiModelProperty(value = "商品名称", example = "稀有道具")
    @Excel(name = "商品名")
    private String itemname;

    /** 商品英文名 */
    @ApiModelProperty(value = "商品英文名", example = "Rare Item")
    @Excel(name = "商品英文名")
    private String hashname;

    /** 提交时间 */
    @ApiModelProperty(value = "提交时间", example = "1648738800000")
    @Excel(name = "提交时间")
    private Long creatTime;

    /** 发货时间 */
    @ApiModelProperty(value = "发货时间", example = "1648738900000")
    @Excel(name = "发货时间")
    private Long claimTime;

    /** 状态 1：发货中  2：已发货 */
    @ApiModelProperty(value = "订单状态", example = "1", notes = "1：发货中 2：已发货")
    @Excel(name = "状态 1：发货中  2：已发货")
    private Long state;
    
    @ApiModelProperty(value = "订单信息", example = "特殊备注信息")
    @Excel(name = "订单信息")
    private String  info;

    /** Steamid */
    @ApiModelProperty(value = "Steam ID", example = "76561198123456789")
    private String steamid;

    /** Steam交易链接 */
    @ApiModelProperty(value = "Steam交易链接", example = "https://steamcommunity.com/tradeoffer/...")
    private String steamlink;

    /** 实际发货金额 */
    @ApiModelProperty(value = "实际发货金额", example = "99.99", notes = "实际发货时的商品价值")
    @Excel(name = "实际发货金额")
    private BigDecimal cost;

    /** 是否自动发货 */
    @ApiModelProperty(value = "是否自动发货", example = "0", notes = "0-否，1-是")
    @Excel(name = "是否自动发货")
    private Integer auto;

    /** 用户手机号搜索条件 */
    @ApiModelProperty(value = "用户手机号搜索", example = "138", notes = "用于搜索的用户手机号，支持模糊匹配")
    private String userPhone;

    /** 发货时间范围查询参数 - 开始时间 */
    @ApiModelProperty(value = "发货时间范围-开始时间", example = "1648738800", notes = "查询条件：发货时间的开始时间戳（秒）")
    private Long beginClaimTime;

    /** 发货时间范围查询参数 - 结束时间 */
    @ApiModelProperty(value = "发货时间范围-结束时间", example = "1648825200", notes = "查询条件：发货时间的结束时间戳（秒）")
    private Long endClaimTime;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("oid", getOid())
            .append("uid", getUid())
            .append("itemid", getItemid())
            .append("itemname", getItemname())
            .append("hashname", getHashname())
            .append("creatTime", getCreatTime())
            .append("claimTime", getClaimTime())
            .append("state", getState())
            .append("steamid", getSteamid())
            .append("steamlink", getSteamlink())
            .append("info", getInfo())
            .append("cost", getCost())
            .append("auto", getAuto())
            .append("beginClaimTime", getBeginClaimTime())
            .append("endClaimTime", getEndClaimTime())
            .toString();
    }
}
